"""
智谱AI Web搜索客户端
负责与 zhipu-web-search-sse 服务通信

提供完整的智谱搜索功能，包括：
- SSE (Server-Sent Events) 连接
- HTTP/HTTPS 请求处理
- 自动重连机制
- 错误处理和回退
- 智谱AI官方API集成
"""

import json
import asyncio
import time
import threading
import queue
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger

try:
    import httpx
    import sseclient  # 用于SSE连接
    ASYNC_LIBS_AVAILABLE = True
    logger.info("异步库加载成功 (httpx, sseclient)")
except ImportError as e:
    ASYNC_LIBS_AVAILABLE = False
    httpx = None
    sseclient = None
    logger.warning(f"异步库未安装，智谱搜索客户端功能受限: {e}")
    logger.info("请安装异步库: pip install httpx sseclient-py")


class ZhipuConnectionState(Enum):
    """智谱搜索连接状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"


@dataclass
class ZhipuSearchRequest:
    """智谱搜索请求数据结构"""
    id: str
    query: str
    params: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    retries: int = 0
    callback: Optional[Callable] = None


@dataclass
class ZhipuSearchResponse:
    """智谱搜索响应数据结构"""
    id: str
    results: List[Dict[str, Any]] = field(default_factory=list)
    error: Optional[Dict[str, Any]] = None
    timestamp: float = field(default_factory=time.time)
    total_results: int = 0
    search_time: float = 0.0


class ZhipuConnectionPool:
    """智谱AI连接池管理器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.pool_size = config.get('connection_pool_size', 5)
        self.base_url = config.get('server_url', 'https://open.bigmodel.cn/api/mcp/web_search/sse')
        self.api_key = config.get('api_key', '')
        self.timeout = config.get('timeout', 30)

        # 连接池
        self._connections: List[Optional[httpx.AsyncClient]] = [None] * self.pool_size
        self._connection_states: List[ZhipuConnectionState] = [ZhipuConnectionState.DISCONNECTED] * self.pool_size
        self._lock = threading.Lock()

        # 请求队列
        self._request_queue = queue.Queue()
        self._response_futures: Dict[str, asyncio.Future] = {}

    async def get_connection(self) -> Optional[httpx.AsyncClient]:
        """获取可用连接"""
        with self._lock:
            for i, (conn, state) in enumerate(zip(self._connections, self._connection_states)):
                if state == ZhipuConnectionState.CONNECTED and conn:
                    return conn
                elif state == ZhipuConnectionState.DISCONNECTED:
                    # 尝试创建新连接
                    try:
                        new_conn = await self._create_connection()
                        self._connections[i] = new_conn
                        self._connection_states[i] = ZhipuConnectionState.CONNECTED
                        return new_conn
                    except Exception as e:
                        logger.error(f"创建智谱AI连接失败: {e}")
                        self._connection_states[i] = ZhipuConnectionState.ERROR
        return None

    async def _create_connection(self) -> httpx.AsyncClient:
        """创建新的HTTP客户端连接"""
        if not ASYNC_LIBS_AVAILABLE:
            raise Exception("异步库未安装，请运行: pip install httpx sseclient-py")

        if httpx is None:
            raise Exception("httpx库未正确导入")

        # 构建请求头
        headers = {
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'User-Agent': 'ZhipuAI-MCP-Client/1.0'
        }

        try:
            client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                headers=headers,
                follow_redirects=True
            )
            return client
        except Exception as e:
            logger.error(f"HTTP客户端创建失败: {e}")
            raise


class ZhipuSearchClient:
    """智谱AI Web搜索客户端，支持连接池和官方SSE API集成"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化智谱搜索客户端

        Args:
            config: 智谱搜索配置
        """
        self.config = config or {}
        self.base_url = self.config.get('server_url', 'https://open.bigmodel.cn/api/mcp/web_search/sse')
        self.api_key = self.config.get('api_key', '')
        self.timeout = self.config.get('timeout', 30)
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 1.0)

        # 连接池
        self.connection_pool = None
        if ASYNC_LIBS_AVAILABLE:
            self.connection_pool = ZhipuConnectionPool(self.config)

        # 连接状态
        self.http_client = None
        self.connection_state = ZhipuConnectionState.DISCONNECTED

        # 请求管理
        self.request_id = 0
        self._pending_requests: Dict[str, ZhipuSearchRequest] = {}

        # 智谱搜索特定配置
        self.zhipu_config = self.config.get('zhipu_search', {})
    
    async def connect(self) -> bool:
        """
        初始化HTTP客户端连接

        Returns:
            bool: 连接是否成功
        """
        if not ASYNC_LIBS_AVAILABLE:
            logger.error("异步库未安装，无法连接智谱搜索服务")
            logger.info("请安装异步库: pip install httpx sseclient-py")
            return False

        if httpx is None:
            logger.error("httpx库未正确导入")
            return False

        self.connection_state = ZhipuConnectionState.CONNECTING

        try:
            logger.info(f"初始化智谱搜索客户端: {self.base_url}")

            # 构建请求头
            headers = {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'User-Agent': 'ZhipuAI-Search-Client/1.0'
            }

            # 创建HTTP客户端
            self.http_client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                headers=headers,
                follow_redirects=True
            )

            self.connection_state = ZhipuConnectionState.CONNECTED
            logger.info("智谱搜索客户端初始化成功")

            return True

        except Exception as e:
            logger.error(f"智谱搜索客户端初始化失败: {e}")
            self.connection_state = ZhipuConnectionState.ERROR
            return False

    async def disconnect(self):
        """断开HTTP客户端连接"""
        self.connection_state = ZhipuConnectionState.DISCONNECTED

        # 关闭HTTP客户端连接
        if self.http_client:
            try:
                await self.http_client.aclose()
                logger.info("智谱搜索客户端连接已断开")
            except Exception as e:
                logger.error(f"断开智谱搜索客户端连接时出错: {e}")
            finally:
                self.http_client = None

        # 清理待处理的请求
        for request_id, request in self._pending_requests.items():
            logger.warning(f"清理未完成的请求: {request_id}")
        self._pending_requests.clear()
    
    async def _send_search_request(self, query: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送搜索请求到智谱AI SSE API

        Args:
            query: 搜索查询
            params: 搜索参数

        Returns:
            Dict[str, Any]: 搜索结果
        """
        if self.connection_state != ZhipuConnectionState.CONNECTED:
            if not await self.connect():
                raise Exception("无法连接到智谱搜索服务")

        if not self.api_key:
            raise Exception("智谱AI API密钥未配置")

        # 构建请求URL（按照官方文档格式）
        url = f"{self.base_url}?Authorization={self.api_key}"

        # 构建请求数据
        request_data = {
            "query": query,
            **params
        }

        try:
            logger.debug(f"发送智谱搜索请求: {query}")

            # 发送POST请求到SSE端点
            response = await self.http_client.post(
                url,
                json=request_data,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream'
                }
            )

            response.raise_for_status()

            # 处理SSE响应
            results = []
            async for line in response.aiter_lines():
                if line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])  # 移除 'data: ' 前缀
                        if 'results' in data:
                            results.extend(data['results'])
                        elif 'result' in data:
                            results.append(data['result'])
                    except json.JSONDecodeError:
                        continue

            logger.debug(f"收到智谱搜索响应: {len(results)} 个结果")
            return {'results': results}

        except httpx.TimeoutException:
            logger.error(f"智谱搜索请求超时: {query}")
            raise Exception("智谱搜索请求超时")
        except httpx.HTTPStatusError as e:
            logger.error(f"智谱搜索HTTP错误: {e.response.status_code}")
            raise Exception(f"智谱搜索HTTP错误: {e.response.status_code}")
        except Exception as e:
            logger.error(f"智谱搜索请求失败: {e}")
            raise
    
    async def search_web(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        使用智谱AI Web搜索执行网络搜索

        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
                - max_results: 最大结果数 (默认: 5)
                - language: 搜索语言 (默认: 'zh')
                - region: 搜索地区 (默认: 'CN')
                - search_type: 搜索类型 (默认: 'general')
                - time_range: 时间范围 (可选)
                - safe_search: 安全搜索 (默认: 'moderate')

        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        # 构建智谱搜索特定的参数
        params = {
            "num_results": kwargs.get('max_results', 5),
            "language": kwargs.get('language', 'zh'),
            "region": kwargs.get('region', 'CN'),
            "search_type": kwargs.get('search_type', 'general'),
            "safe_search": kwargs.get('safe_search', 'moderate')
        }

        # 添加可选参数
        if 'time_range' in kwargs:
            params['time_range'] = kwargs['time_range']

        # 合并智谱特定配置
        params.update(self.zhipu_config)

        try:
            # 使用智谱AI SSE API执行搜索
            result = await self._send_search_request(query, params)

            # 处理智谱搜索的响应格式
            if isinstance(result, dict):
                # 如果返回的是包装的结果
                search_results = result.get('results', result.get('data', []))
            elif isinstance(result, list):
                # 如果直接返回结果列表
                search_results = result
            else:
                logger.warning(f"未知的搜索结果格式: {type(result)}")
                return []

            # 标准化结果格式
            standardized_results = []
            for item in search_results:
                standardized_item = self._standardize_search_result(item)
                if standardized_item:
                    standardized_results.append(standardized_item)

            logger.info(f"智谱AI搜索完成，找到 {len(standardized_results)} 个结果")
            return standardized_results

        except Exception as e:
            logger.error(f"智谱AI搜索失败: {e}")
            return []

    def _standardize_search_result(self, raw_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        标准化搜索结果格式，适配智谱AI搜索的响应格式

        Args:
            raw_result: 原始搜索结果

        Returns:
            Optional[Dict[str, Any]]: 标准化后的搜索结果
        """
        try:
            # 处理智谱AI搜索的结果格式
            standardized = {
                'title': raw_result.get('title', ''),
                'url': raw_result.get('url', raw_result.get('link', '')),
                'snippet': raw_result.get('snippet', raw_result.get('description', raw_result.get('content', ''))),
                'source': raw_result.get('source', raw_result.get('domain', 'Unknown')),
                'publish_date': raw_result.get('publish_date', raw_result.get('date', None))
            }

            # 验证必需字段
            if not standardized['title'] or not standardized['url']:
                logger.debug(f"搜索结果缺少必需字段: {raw_result}")
                return None

            return standardized

        except Exception as e:
            logger.error(f"标准化搜索结果失败: {e}")
            return None
    
    def search_web_sync(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        同步版本的网络搜索
        
        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        if not ASYNC_LIBS_AVAILABLE:
            logger.error("异步库未安装，无法执行MCP搜索")
            return []
        
        try:
            # 创建新的事件循环或使用现有的
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果循环正在运行，创建新的任务
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(
                            lambda: asyncio.run(self.search_web(query, **kwargs))
                        )
                        return future.result(timeout=self.timeout)
                else:
                    return loop.run_until_complete(self.search_web(query, **kwargs))
            except RuntimeError:
                # 没有事件循环，创建新的
                return asyncio.run(self.search_web(query, **kwargs))
                
        except Exception as e:
            logger.error(f"同步网络搜索失败: {e}")
            return []
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
        return False


class FallbackWebSearcher:
    """备用网络搜索器（当MCP不可用时使用）"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化备用搜索器
        
        Args:
            config: 搜索配置
        """
        self.config = config or {}
        self.timeout = self.config.get('timeout', 30)
        self.user_agent = self.config.get(
            'user_agent', 
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
    
    def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        执行备用搜索
        
        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            import requests
            from bs4 import BeautifulSoup
            import urllib.parse
            
            # 构建搜索URL（使用DuckDuckGo作为备用）
            encoded_query = urllib.parse.quote_plus(query)
            search_url = f"https://duckduckgo.com/html/?q={encoded_query}"
            
            headers = {
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }
            
            # 发送请求
            response = requests.get(search_url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析结果
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # 查找搜索结果
            result_elements = soup.find_all('div', class_='result')
            max_results = kwargs.get('max_results', 5)
            
            for element in result_elements[:max_results]:
                try:
                    # 提取标题
                    title_elem = element.find('a', class_='result__a')
                    title = title_elem.get_text(strip=True) if title_elem else ''
                    
                    # 提取URL
                    url = title_elem.get('href', '') if title_elem else ''
                    
                    # 提取摘要
                    snippet_elem = element.find('a', class_='result__snippet')
                    snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''
                    
                    if title and url:
                        results.append({
                            'title': title,
                            'url': url,
                            'snippet': snippet,
                            'source': 'DuckDuckGo',
                            'publish_date': None
                        })
                        
                except Exception as e:
                    logger.debug(f"解析搜索结果项失败: {e}")
                    continue
            
            logger.info(f"备用搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"备用搜索失败: {e}")
            return []


# 向后兼容性别名
MCPClient = ZhipuSearchClient
MCPConnectionState = ZhipuConnectionState
MCPRequest = ZhipuSearchRequest
MCPResponse = ZhipuSearchResponse
MCPConnectionPool = ZhipuConnectionPool
