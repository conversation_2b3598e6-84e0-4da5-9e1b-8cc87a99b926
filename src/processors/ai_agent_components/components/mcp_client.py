"""
MCP (Model Context Protocol) 客户端
负责与 zhipu-web-search-sse MCP 服务器通信

提供完整的MCP客户端功能，包括：
- 连接池管理
- 自动重连机制
- 心跳检测
- 错误处理和回退
- zhipu-web-search-sse 特定集成
"""

import json
import asyncio
import time
import threading
import queue
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger

try:
    import httpx
    import websockets
    ASYNC_LIBS_AVAILABLE = True
    logger.info("异步库加载成功 (httpx, websockets)")
except ImportError as e:
    ASYNC_LIBS_AVAILABLE = False
    httpx = None
    websockets = None
    logger.warning(f"异步库未安装，MCP客户端功能受限: {e}")
    logger.info("请安装异步库: pip install httpx websockets")


class MCPConnectionState(Enum):
    """MCP连接状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"


@dataclass
class MCPRequest:
    """MCP请求数据结构"""
    id: str
    method: str
    params: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    retries: int = 0
    callback: Optional[Callable] = None


@dataclass
class MCPResponse:
    """MCP响应数据结构"""
    id: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None
    timestamp: float = field(default_factory=time.time)


class MCPConnectionPool:
    """MCP连接池管理器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.pool_size = config.get('connection_pool_size', 5)
        self.server_url = config.get('server_url', 'ws://localhost:8080')
        self.api_key = config.get('api_key', '')
        self.timeout = config.get('timeout', 30)

        # 连接池
        self._connections: List[Optional[Any]] = [None] * self.pool_size
        self._connection_states: List[MCPConnectionState] = [MCPConnectionState.DISCONNECTED] * self.pool_size
        self._lock = threading.Lock()

        # 请求队列
        self._request_queue = queue.Queue()
        self._response_futures: Dict[str, asyncio.Future] = {}

    async def get_connection(self) -> Optional[Any]:
        """获取可用连接"""
        with self._lock:
            for i, (conn, state) in enumerate(zip(self._connections, self._connection_states)):
                if state == MCPConnectionState.CONNECTED and conn:
                    return conn
                elif state == MCPConnectionState.DISCONNECTED:
                    # 尝试创建新连接
                    try:
                        new_conn = await self._create_connection()
                        self._connections[i] = new_conn
                        self._connection_states[i] = MCPConnectionState.CONNECTED
                        return new_conn
                    except Exception as e:
                        logger.error(f"创建MCP连接失败: {e}")
                        self._connection_states[i] = MCPConnectionState.ERROR
        return None

    async def _create_connection(self) -> Any:
        """创建新的WebSocket连接"""
        if not ASYNC_LIBS_AVAILABLE:
            raise Exception("异步库未安装，请运行: pip install websockets httpx")

        if websockets is None:
            raise Exception("websockets库未正确导入")

        headers = {}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'

        try:
            return await websockets.connect(
                self.server_url,
                additional_headers=headers,
                open_timeout=self.timeout
            )
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            raise


class MCPClient:
    """增强版MCP客户端，支持连接池和zhipu-web-search-sse集成"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化 MCP 客户端

        Args:
            config: MCP 配置
        """
        self.config = config or {}
        self.server_url = self.config.get('server_url', 'ws://localhost:8080')
        self.api_key = self.config.get('api_key', '')
        self.timeout = self.config.get('timeout', 30)
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 1.0)
        self.heartbeat_interval = self.config.get('heartbeat_interval', 30)

        # 连接池
        self.connection_pool = None
        if ASYNC_LIBS_AVAILABLE:
            self.connection_pool = MCPConnectionPool(self.config)

        # 连接状态
        self.websocket = None
        self.connection_state = MCPConnectionState.DISCONNECTED

        # 请求管理
        self.request_id = 0
        self._pending_requests: Dict[str, MCPRequest] = {}

        # 心跳任务
        self._heartbeat_task = None

        # zhipu-web-search-sse 特定配置
        self.zhipu_config = self.config.get('zhipu_search', {})
    
    async def connect(self) -> bool:
        """
        连接到 MCP 服务器，支持自动重连

        Returns:
            bool: 连接是否成功
        """
        if not ASYNC_LIBS_AVAILABLE:
            logger.error("异步库未安装，无法连接MCP服务器")
            logger.info("请安装异步库: pip install websockets httpx")
            return False

        if websockets is None:
            logger.error("websockets库未正确导入")
            return False

        self.connection_state = MCPConnectionState.CONNECTING

        for attempt in range(self.max_retries):
            try:
                logger.info(f"尝试连接到MCP服务器: {self.server_url} (第{attempt + 1}次)")

                # 构建连接头
                headers = {}
                if self.api_key:
                    headers['Authorization'] = f'Bearer {self.api_key}'

                # 连接WebSocket
                self.websocket = await websockets.connect(
                    self.server_url,
                    additional_headers=headers,
                    open_timeout=self.timeout
                )

                self.connection_state = MCPConnectionState.CONNECTED
                logger.info("MCP服务器连接成功")

                # 启动心跳检测
                await self._start_heartbeat()

                return True

            except Exception as e:
                logger.error(f"连接MCP服务器失败 (第{attempt + 1}次): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))

        self.connection_state = MCPConnectionState.ERROR
        return False

    async def _start_heartbeat(self):
        """启动心跳检测任务"""
        if self._heartbeat_task:
            self._heartbeat_task.cancel()

        self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())

    async def _heartbeat_loop(self):
        """心跳检测循环"""
        while self.connection_state == MCPConnectionState.CONNECTED:
            try:
                await asyncio.sleep(self.heartbeat_interval)

                # 发送心跳ping
                if self.websocket:
                    await self.websocket.ping()
                    logger.debug("发送心跳检测")

            except Exception as e:
                logger.error(f"心跳检测失败: {e}")
                self.connection_state = MCPConnectionState.ERROR
                break
    
    async def disconnect(self):
        """断开与 MCP 服务器的连接"""
        self.connection_state = MCPConnectionState.DISCONNECTED

        # 停止心跳检测
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
            self._heartbeat_task = None

        # 关闭WebSocket连接
        if self.websocket:
            try:
                await self.websocket.close()
                logger.info("MCP服务器连接已断开")
            except Exception as e:
                logger.error(f"断开MCP服务器连接时出错: {e}")
            finally:
                self.websocket = None

        # 清理待处理的请求
        for request_id, request in self._pending_requests.items():
            logger.warning(f"清理未完成的请求: {request_id}")
        self._pending_requests.clear()
    
    async def send_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送请求到 MCP 服务器
        
        Args:
            method: 方法名
            params: 参数
            
        Returns:
            Dict[str, Any]: 响应数据
        """
        if self.connection_state != MCPConnectionState.CONNECTED:
            if not await self.connect():
                raise Exception("无法连接到MCP服务器")
        
        # 构建请求
        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method,
            "params": params
        }
        
        try:
            # 发送请求
            await self.websocket.send(json.dumps(request))
            logger.debug(f"发送MCP请求: {method}")
            
            # 等待响应
            response_text = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=self.timeout
            )
            
            response = json.loads(response_text)
            logger.debug(f"收到MCP响应: {response.get('id')}")
            
            # 检查错误
            if 'error' in response:
                error = response['error']
                raise Exception(f"MCP错误: {error.get('message', '未知错误')}")
            
            return response.get('result', {})
            
        except asyncio.TimeoutError:
            logger.error(f"MCP请求超时: {method}")
            raise Exception("MCP请求超时")
        except Exception as e:
            logger.error(f"MCP请求失败: {e}")
            raise
    
    async def search_web(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        使用zhipu-web-search-sse MCP执行网络搜索

        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
                - max_results: 最大结果数 (默认: 5)
                - language: 搜索语言 (默认: 'zh')
                - region: 搜索地区 (默认: 'CN')
                - search_type: 搜索类型 (默认: 'general')
                - time_range: 时间范围 (可选)
                - safe_search: 安全搜索 (默认: 'moderate')

        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        # 构建zhipu-web-search-sse特定的参数
        params = {
            "query": query,
            "num_results": kwargs.get('max_results', 5),
            "language": kwargs.get('language', 'zh'),
            "region": kwargs.get('region', 'CN'),
            "search_type": kwargs.get('search_type', 'general'),
            "safe_search": kwargs.get('safe_search', 'moderate')
        }

        # 添加可选参数
        if 'time_range' in kwargs:
            params['time_range'] = kwargs['time_range']

        # 合并zhipu特定配置
        params.update(self.zhipu_config)

        try:
            # 使用zhipu-web-search-sse MCP的search方法
            result = await self.send_request("search", params)

            # 处理zhipu-web-search-sse的响应格式
            if isinstance(result, dict):
                # 如果返回的是包装的结果
                search_results = result.get('results', result.get('data', []))
            elif isinstance(result, list):
                # 如果直接返回结果列表
                search_results = result
            else:
                logger.warning(f"未知的搜索结果格式: {type(result)}")
                return []

            # 标准化结果格式
            standardized_results = []
            for item in search_results:
                standardized_item = self._standardize_search_result(item)
                if standardized_item:
                    standardized_results.append(standardized_item)

            logger.info(f"zhipu-web-search-sse搜索完成，找到 {len(standardized_results)} 个结果")
            return standardized_results

        except Exception as e:
            logger.error(f"zhipu-web-search-sse搜索失败: {e}")
            return []

    def _standardize_search_result(self, raw_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        标准化搜索结果格式，适配不同MCP服务器的响应格式

        Args:
            raw_result: 原始搜索结果

        Returns:
            Optional[Dict[str, Any]]: 标准化后的搜索结果
        """
        try:
            # 处理zhipu-web-search-sse的结果格式
            standardized = {
                'title': raw_result.get('title', ''),
                'url': raw_result.get('url', raw_result.get('link', '')),
                'snippet': raw_result.get('snippet', raw_result.get('description', raw_result.get('content', ''))),
                'source': raw_result.get('source', raw_result.get('domain', 'Unknown')),
                'publish_date': raw_result.get('publish_date', raw_result.get('date', None))
            }

            # 验证必需字段
            if not standardized['title'] or not standardized['url']:
                logger.debug(f"搜索结果缺少必需字段: {raw_result}")
                return None

            return standardized

        except Exception as e:
            logger.error(f"标准化搜索结果失败: {e}")
            return None
    
    def search_web_sync(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        同步版本的网络搜索
        
        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        if not ASYNC_LIBS_AVAILABLE:
            logger.error("异步库未安装，无法执行MCP搜索")
            return []
        
        try:
            # 创建新的事件循环或使用现有的
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果循环正在运行，创建新的任务
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(
                            lambda: asyncio.run(self.search_web(query, **kwargs))
                        )
                        return future.result(timeout=self.timeout)
                else:
                    return loop.run_until_complete(self.search_web(query, **kwargs))
            except RuntimeError:
                # 没有事件循环，创建新的
                return asyncio.run(self.search_web(query, **kwargs))
                
        except Exception as e:
            logger.error(f"同步网络搜索失败: {e}")
            return []
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
        return False


class FallbackWebSearcher:
    """备用网络搜索器（当MCP不可用时使用）"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化备用搜索器
        
        Args:
            config: 搜索配置
        """
        self.config = config or {}
        self.timeout = self.config.get('timeout', 30)
        self.user_agent = self.config.get(
            'user_agent', 
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
    
    def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        执行备用搜索
        
        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            import requests
            from bs4 import BeautifulSoup
            import urllib.parse
            
            # 构建搜索URL（使用DuckDuckGo作为备用）
            encoded_query = urllib.parse.quote_plus(query)
            search_url = f"https://duckduckgo.com/html/?q={encoded_query}"
            
            headers = {
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }
            
            # 发送请求
            response = requests.get(search_url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析结果
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # 查找搜索结果
            result_elements = soup.find_all('div', class_='result')
            max_results = kwargs.get('max_results', 5)
            
            for element in result_elements[:max_results]:
                try:
                    # 提取标题
                    title_elem = element.find('a', class_='result__a')
                    title = title_elem.get_text(strip=True) if title_elem else ''
                    
                    # 提取URL
                    url = title_elem.get('href', '') if title_elem else ''
                    
                    # 提取摘要
                    snippet_elem = element.find('a', class_='result__snippet')
                    snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''
                    
                    if title and url:
                        results.append({
                            'title': title,
                            'url': url,
                            'snippet': snippet,
                            'source': 'DuckDuckGo',
                            'publish_date': None
                        })
                        
                except Exception as e:
                    logger.debug(f"解析搜索结果项失败: {e}")
                    continue
            
            logger.info(f"备用搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"备用搜索失败: {e}")
            return []
