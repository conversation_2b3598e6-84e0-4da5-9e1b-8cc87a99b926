# MCP (Model Context Protocol) 集成配置示例
# 展示如何配置zhipu-web-search-sse MCP客户端

# 邮箱配置
email:
  host: "imap.qq.com"
  port: 993
  username: "<EMAIL>"
  password: "your_password"
  use_ssl: true
  protocol: "imap"
  
  # SMTP 发送配置
  smtp:
    host: "smtp.qq.com"
    port: 587
    username: "<EMAIL>"
    password: "your_password"
    use_tls: true
    from_name: "AI邮件助手"

# AI分析配置（包含MCP配置）
ai:
  provider: "openai"
  api_key: "your_api_key"
  model: "gpt-3.5-turbo"
  base_url: "https://api.openai.com/v1"
  max_tokens: 1000
  temperature: 0.7
  
  # MCP客户端配置
  mcp:
    enabled: true                           # 是否启用MCP功能
    server_url: "ws://localhost:8080"       # MCP服务器WebSocket地址
    api_key: "your_mcp_api_key"            # MCP服务器API密钥（可选）
    timeout: 30                            # 连接超时时间(秒)
    max_retries: 3                         # 最大重试次数
    retry_delay: 1.0                       # 重试延迟(秒)
    connection_pool_size: 5                # 连接池大小
    heartbeat_interval: 30                 # 心跳检测间隔(秒)
    
    # zhipu-web-search-sse 特定配置
    zhipu_search:
      # 搜索引擎偏好设置
      preferred_engines:
        - "google"
        - "bing"
        - "baidu"
      
      # 搜索结果过滤
      content_filter:
        min_content_length: 50             # 最小内容长度
        exclude_domains:                   # 排除的域名
          - "spam-site.com"
          - "low-quality.net"
        include_file_types:                # 包含的文件类型
          - "html"
          - "pdf"
          - "doc"
      
      # 搜索质量控制
      quality_settings:
        relevance_threshold: 0.6           # 相关性阈值
        freshness_weight: 0.2              # 新鲜度权重
        authority_weight: 0.3              # 权威性权重
        
      # 缓存设置
      cache:
        enabled: true                      # 启用搜索结果缓存
        ttl: 3600                         # 缓存过期时间(秒)
        max_entries: 1000                 # 最大缓存条目数
    
    # 备用搜索配置（当MCP不可用时）
    fallback:
      enabled: true                        # 启用备用搜索
      search_engine: "duckduckgo"         # 备用搜索引擎
      timeout: 15                         # 备用搜索超时
      max_results: 5                      # 备用搜索最大结果数
      user_agent: "Mozilla/5.0 (compatible; EmailBot/1.0)"

# 调度器配置
scheduler:
  check_interval: 60
  max_emails_per_check: 50
  enable_scheduler: true

# 日志配置
log:
  level: "INFO"
  file_path: "logs/mailer.log"
  max_size: "10 MB"
  retention: "30 days"

# 处理器配置
processors:
  # AI回复处理器（升级版AI Agent系统）
  ai_reply_processor:
    enabled: true
    
    # 搜索处理器配置
    search_handler:
      max_results: 5                      # 最大搜索结果数
      timeout: 30                         # 搜索超时时间
      language: "zh"                      # 默认搜索语言
      region: "CN"                        # 默认搜索地区
      
      # 搜索质量评估权重
      quality_weights:
        title_relevance: 0.3              # 标题相关性权重
        snippet_relevance: 0.4            # 摘要相关性权重
        source_authority: 0.2             # 来源权威性权重
        freshness: 0.1                    # 新鲜度权重
      
      # MCP配置（继承自ai.mcp，可覆盖）
      mcp:
        enabled: true                     # 是否启用MCP搜索
        fallback_on_error: true           # 错误时是否回退到备用搜索
        
    # 分析引擎配置
    analysis_engine:
      intent_classification:
        confidence_threshold: 0.7         # 意图识别置信度阈值
        max_categories: 3                 # 最大分类数
        
      search_analysis:
        relevance_threshold: 0.6          # 搜索结果相关性阈值
        max_analysis_results: 10          # 最大分析结果数
        
    # 任务执行器配置
    task_executor:
      max_concurrent_tasks: 3             # 最大并发任务数
      task_timeout: 120                   # 任务超时时间(秒)
      
      # 任务步骤配置
      steps:
        search_keywords_extraction:
          max_keywords: 10                # 最大关键词数
          min_keyword_length: 2           # 最小关键词长度
          
        search_execution:
          retry_on_failure: true          # 失败时重试
          max_retries: 2                  # 最大重试次数
          
        result_analysis:
          deep_analysis: true             # 启用深度分析
          sentiment_analysis: true        # 启用情感分析
          
    # 回复生成器配置
    response_generator:
      max_reply_length: 1000              # 最大回复长度
      include_sources: true               # 包含来源信息
      professional_tone: true             # 专业语调
      language: "zh"                      # 回复语言
      
      # 回复模板
      templates:
        simple: "感谢您的邮件。{content}"
        search_based: "根据我的搜索和分析，{content}"
        analytical: "基于详细分析，{content}"
        solution: "针对您的问题，{content}"
        research: "基于研究结果，{content}"

# 环境变量映射
# 以下配置项可以通过环境变量覆盖：
# MCP_ENABLED=true/false
# MCP_SERVER_URL=ws://your-mcp-server:8080
# MCP_API_KEY=your_mcp_api_key
# MCP_TIMEOUT=30
# MCP_MAX_RETRIES=3
# MCP_CONNECTION_POOL_SIZE=5
# MCP_HEARTBEAT_INTERVAL=30

# 安全配置
security:
  # MCP连接安全
  mcp_security:
    verify_ssl: true                      # 验证SSL证书
    allowed_hosts:                        # 允许的MCP服务器主机
      - "localhost"
      - "your-trusted-mcp-server.com"
    connection_encryption: true           # 启用连接加密
    
  # API密钥管理
  api_key_management:
    use_environment_variables: true       # 优先使用环境变量
    encrypt_config_file: false            # 是否加密配置文件中的密钥
    key_rotation_enabled: false           # 是否启用密钥轮换

# 监控和指标
monitoring:
  mcp_metrics:
    enabled: true                         # 启用MCP指标收集
    metrics_interval: 60                  # 指标收集间隔(秒)
    
    # 监控指标
    tracked_metrics:
      - "connection_count"                # 连接数
      - "request_count"                   # 请求数
      - "response_time"                   # 响应时间
      - "error_rate"                      # 错误率
      - "search_success_rate"             # 搜索成功率
      
    # 告警阈值
    alert_thresholds:
      max_response_time: 30               # 最大响应时间(秒)
      max_error_rate: 0.1                 # 最大错误率
      min_success_rate: 0.9               # 最小成功率

# 开发和调试配置
development:
  debug_mode: false                       # 调试模式
  log_mcp_messages: false                 # 记录MCP消息
  mock_mcp_responses: false               # 模拟MCP响应（用于测试）
  
  # 测试配置
  testing:
    use_test_mcp_server: false            # 使用测试MCP服务器
    test_server_url: "ws://localhost:8081"
    generate_test_data: false             # 生成测试数据
