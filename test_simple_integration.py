#!/usr/bin/env python3
"""
简化的AI Agent系统与智谱AI搜索集成测试
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from loguru import logger
from src.core.config_manager import ConfigManager
from src.processors.ai_agent_components.components.search_handler import SearchHandler

async def test_simple_search_integration():
    """简化的搜索集成测试"""
    logger.info("开始简化的搜索集成测试...")
    
    try:
        # 加载配置
        config_manager = ConfigManager("config/config.yaml")
        config = config_manager.load_config()
        
        # 手动构建搜索配置
        search_config = {
            'max_results': 5,
            'timeout': 30,
            'language': 'zh',
            'region': 'CN',
            'mcp': {
                'enabled': True,
                'fallback_on_error': True,
                'server_url': 'https://open.bigmodel.cn/api/mcp/web_search/sse',
                'api_key': '40bf4a9662c642ff8d78e8f9ffec5de5.nKnd2SY7Ic1FGdv6',
                'timeout': 30,
                'max_retries': 3,
                'retry_delay': 1.0,
                'connection_pool_size': 5
            },
            'quality_weights': {
                'title_relevance': 0.3,
                'snippet_relevance': 0.4,
                'source_authority': 0.2,
                'freshness': 0.1
            }
        }
        
        logger.info(f"搜索配置: MCP启用={search_config['mcp']['enabled']}")
        
        # 创建搜索处理器
        search_handler = SearchHandler(search_config)
        
        # 测试搜索功能
        test_queries = [
            "人工智能在教育领域的应用",
            "区块链技术最新发展"
        ]
        
        for query in test_queries:
            logger.info(f"测试搜索查询: {query}")
            
            try:
                # 执行搜索
                results = await search_handler.search(query)
                
                if results:
                    logger.success(f"✅ 搜索成功，找到 {len(results)} 个结果:")
                    for i, result in enumerate(results[:3], 1):  # 只显示前3个结果
                        logger.info(f"  {i}. {result.get('title', 'N/A')}")
                        logger.info(f"     来源: {result.get('source', 'N/A')}")
                        logger.info(f"     URL: {result.get('url', 'N/A')}")
                        logger.info("")
                else:
                    logger.warning(f"⚠️ 搜索 '{query}' 未返回结果")
                    
            except Exception as e:
                logger.error(f"❌ 搜索 '{query}' 失败: {e}")
            
            # 等待一下避免请求过快
            await asyncio.sleep(2)
        
        logger.success("✅ 简化搜索集成测试完成!")
        
    except Exception as e:
        logger.error(f"❌ 简化搜索集成测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

async def test_search_quality():
    """测试搜索质量评估"""
    logger.info("开始测试搜索质量评估...")
    
    try:
        # 手动构建搜索配置
        search_config = {
            'max_results': 5,
            'timeout': 30,
            'language': 'zh',
            'region': 'CN',
            'mcp': {
                'enabled': True,
                'fallback_on_error': True,
                'server_url': 'https://open.bigmodel.cn/api/mcp/web_search/sse',
                'api_key': '40bf4a9662c642ff8d78e8f9ffec5de5.nKnd2SY7Ic1FGdv6',
                'timeout': 30,
                'max_retries': 3,
                'retry_delay': 1.0,
                'connection_pool_size': 5
            },
            'quality_weights': {
                'title_relevance': 0.3,
                'snippet_relevance': 0.4,
                'source_authority': 0.2,
                'freshness': 0.1
            }
        }
        
        # 创建搜索处理器
        search_handler = SearchHandler(search_config)
        
        # 测试质量评估
        query = "Python机器学习库"
        logger.info(f"测试质量评估查询: {query}")
        
        # 执行搜索
        results = await search_handler.search(query)
        
        if results:
            logger.info(f"搜索到 {len(results)} 个结果，开始质量评估...")
            
            # 评估每个结果的质量
            for i, result in enumerate(results[:3], 1):
                quality_score = search_handler._calculate_quality_score(result, query)
                logger.info(f"结果 {i}: {result.get('title', 'N/A')[:50]}...")
                logger.info(f"  质量评分: {quality_score:.3f}")
                logger.info(f"  来源权威性: {result.get('source', 'N/A')}")
                logger.info("")
            
            logger.success("✅ 搜索质量评估测试完成!")
        else:
            logger.warning("⚠️ 未获取到搜索结果，无法进行质量评估")
            
    except Exception as e:
        logger.error(f"❌ 搜索质量评估测试失败: {e}")

async def test_fallback():
    """测试备用搜索机制"""
    logger.info("开始测试备用搜索机制...")
    
    try:
        # 手动构建搜索配置，禁用MCP
        search_config = {
            'max_results': 5,
            'timeout': 30,
            'language': 'zh',
            'region': 'CN',
            'mcp': {
                'enabled': False,  # 禁用MCP
                'fallback_on_error': True
            },
            'quality_weights': {
                'title_relevance': 0.3,
                'snippet_relevance': 0.4,
                'source_authority': 0.2,
                'freshness': 0.1
            }
        }
        
        logger.info("模拟MCP不可用，测试备用搜索...")
        
        # 创建搜索处理器
        search_handler = SearchHandler(search_config)
        
        # 测试备用搜索
        query = "开源软件发展"
        logger.info(f"测试备用搜索查询: {query}")
        
        results = await search_handler.search(query)
        
        if results:
            logger.success(f"✅ 备用搜索成功，找到 {len(results)} 个结果")
            for i, result in enumerate(results[:2], 1):
                logger.info(f"  {i}. {result.get('title', 'N/A')}")
        else:
            logger.warning("⚠️ 备用搜索未返回结果")
            
        logger.success("✅ 备用搜索机制测试完成!")
        
    except Exception as e:
        logger.error(f"❌ 备用搜索机制测试失败: {e}")

async def main():
    """主测试函数"""
    logger.info("🚀 开始简化的AI Agent系统与智谱AI搜索集成测试")
    logger.info("=" * 70)
    
    # 1. 测试搜索处理器集成
    await test_simple_search_integration()
    
    logger.info("=" * 70)
    
    # 2. 测试搜索质量评估
    await test_search_quality()
    
    logger.info("=" * 70)
    
    # 3. 测试备用搜索机制
    await test_fallback()
    
    logger.info("=" * 70)
    logger.info("🎉 简化的AI Agent系统集成测试完成!")

if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行测试
    asyncio.run(main())
