# 邮件处理相关
email-validator==2.1.0

# 大模型API集成
openai>=1.0.0
anthropic>=0.8.0
requests>=2.31.0

# 网络搜索和MCP支持
httpx>=0.25.0
websockets>=12.0
aiohttp>=3.9.0
beautifulsoup4>=4.12.0
mcp>=1.0.0

# 配置管理
pydantic>=2.5.0
pydantic-settings>=2.1.0
PyYAML>=6.0.0
python-dotenv>=1.0.0

# 任务调度
schedule>=1.2.0
APScheduler>=3.10.0

# 日志管理
loguru>=0.7.0

# 异步处理
aiofiles>=23.0.0

# 工具库
click>=8.1.0
rich>=13.0.0
tqdm>=4.66.0

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.23.0
black>=23.0.0
flake8>=7.0.0

# 安全
cryptography>=41.0.0
keyring>=24.0.0

PyPDF2>=3.0.0  # PDF解析库

docling

pytest
